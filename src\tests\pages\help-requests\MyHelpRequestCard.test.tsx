/**
 * MyHelpRequestCard 组件测试
 * 测试我的求助卡片组件的渲染和交互功能
 */

import { render, screen } from "@testing-library/react";
import { vi, describe, it, expect, beforeEach } from "vitest";
import { useRouter } from "next/navigation";
import { HelpRequest } from "@/types/help-request";

// 由于 MyHelpRequestCard 是在页面文件中定义的内部组件，
// 我们需要模拟整个页面来测试这个组件
vi.mock("next/navigation", () => ({
  useRouter: vi.fn(),
}));

// Mock AuthGuard 组件
vi.mock("@/components/AuthGuard", () => {
  return function MockAuthGuard({ children }: { children: React.ReactNode }) {
    return <div data-testid="auth-guard">{children}</div>;
  };
});

// Mock 服务
vi.mock("@/services/helpRequestService", () => ({
  getUserHelpRequests: vi.fn(),
  getUserAnswers: vi.fn(),
  getUserStats: vi.fn(),
}));

// Mock ToastProvider
vi.mock("@/components/ToastProvider", () => ({
  useToast: () => ({
    showToast: vi.fn(),
  }),
}));

// Mock 数据
const mockHelpRequest: HelpRequest = {
  id: "1",
  title: "寻找《测试电影》高清资源",
  description: "需要找到这部电影的高清版本，最好是1080p以上",
  status: "open",
  pan_types: ["baidu", "quark"],
  resource_types: ["video"],
  user: {
    id: "user1",
    username: "testuser",
    level: 2,
  },
  answers_count: 3,
  view_count: 156,
  created_at: "2024-01-15T10:30:00Z",
  updated_at: "2024-01-15T10:30:00Z",
};

const mockPush = vi.fn();

beforeEach(() => {
  (useRouter as any).mockReturnValue({
    push: mockPush,
  });
  vi.clearAllMocks();
});

// 创建一个测试组件来包装 MyHelpRequestCard
function TestMyHelpRequestCard({ helpRequest }: { helpRequest: HelpRequest }) {
  // 复制 MyHelpRequestCard 的逻辑
  const getStatusColor = (status: string) => {
    switch (status) {
      case "open":
        return "bg-green-100 text-green-800 dark:bg-green-900/20 dark:text-green-400";
      case "solved":
        return "bg-blue-100 text-blue-800 dark:bg-blue-900/20 dark:text-blue-400";
      case "closed":
        return "bg-gray-100 text-gray-800 dark:bg-gray-900/20 dark:text-gray-400";
      default:
        return "bg-gray-100 text-gray-800 dark:bg-gray-900/20 dark:text-gray-400";
    }
  };

  const getStatusText = (status: string) => {
    switch (status) {
      case "open":
        return "求助中";
      case "solved":
        return "已解决";
      case "closed":
        return "已关闭";
      default:
        return "未知";
    }
  };

  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    const now = new Date();
    const diffTime = Math.abs(now.getTime() - date.getTime());
    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));

    if (diffDays === 1) {
      return "今天";
    } else if (diffDays <= 7) {
      return `${diffDays}天前`;
    } else {
      return date.toLocaleDateString("zh-CN");
    }
  };

  return (
    <a
      href={`/help-requests/${helpRequest.id}`}
      className="block bg-card-background border border-border-color rounded-lg p-6 hover:shadow-lg hover:border-blue-300 dark:hover:border-blue-600 transition-all duration-200 cursor-pointer"
    >
      <div className="flex items-start justify-between mb-3">
        <h3 className="text-lg font-semibold text-gray-900 dark:text-gray-100 hover:text-blue-600 transition-colors line-clamp-2 flex-1">
          {helpRequest.title}
        </h3>
        <span
          className={`px-2 py-1 rounded-full text-xs font-medium whitespace-nowrap ml-3 ${getStatusColor(
            helpRequest.status
          )}`}
        >
          {getStatusText(helpRequest.status)}
        </span>
      </div>

      {helpRequest.description && (
        <p className="text-secondary-text text-sm mb-3 line-clamp-2">
          {helpRequest.description}
        </p>
      )}

      <div className="flex flex-wrap gap-2 mb-3">
        {helpRequest.pan_types.slice(0, 3).map((panType) => (
          <span
            key={panType}
            className="px-2 py-1 bg-blue-100 text-blue-800 dark:bg-blue-900/20 dark:text-blue-400 rounded text-xs"
          >
            {panType === "baidu"
              ? "百度网盘"
              : panType === "quark"
              ? "夸克网盘"
              : panType}
          </span>
        ))}
        {helpRequest.pan_types.length > 3 && (
          <span className="px-2 py-1 bg-gray-100 text-gray-800 dark:bg-gray-900/20 dark:text-gray-400 rounded text-xs">
            +{helpRequest.pan_types.length - 3}
          </span>
        )}
      </div>

      <div className="flex items-center justify-between text-sm text-secondary-text">
        <div className="flex items-center space-x-4">
          <div className="flex items-center">
            <span>{helpRequest.answers_count} 个回答</span>
          </div>
          <div className="flex items-center">
            <span>{helpRequest.view_count} 次浏览</span>
          </div>
        </div>
        <span>{formatDate(helpRequest.created_at)}</span>
      </div>
    </a>
  );
}

describe("MyHelpRequestCard", () => {
  it("应该正确渲染我的求助卡片的基本信息", () => {
    render(<TestMyHelpRequestCard helpRequest={mockHelpRequest} />);

    // 检查标题
    expect(screen.getByText("寻找《测试电影》高清资源")).toBeInTheDocument();

    // 检查描述
    expect(
      screen.getByText("需要找到这部电影的高清版本，最好是1080p以上")
    ).toBeInTheDocument();

    // 检查状态
    expect(screen.getByText("求助中")).toBeInTheDocument();

    // 检查统计信息
    expect(screen.getByText("3 个回答")).toBeInTheDocument();
    expect(screen.getByText("156 次浏览")).toBeInTheDocument();
  });

  it("应该正确渲染网盘类型标签", () => {
    render(<TestMyHelpRequestCard helpRequest={mockHelpRequest} />);

    expect(screen.getByText("百度网盘")).toBeInTheDocument();
    expect(screen.getByText("夸克网盘")).toBeInTheDocument();
  });

  it("应该在状态为已解决时显示正确的状态", () => {
    const solvedRequest = {
      ...mockHelpRequest,
      status: "solved" as const,
    };

    render(<TestMyHelpRequestCard helpRequest={solvedRequest} />);

    expect(screen.getByText("已解决")).toBeInTheDocument();
  });

  it("应该整个卡片都可以点击", () => {
    render(<TestMyHelpRequestCard helpRequest={mockHelpRequest} />);

    const cardLink = screen.getByRole("link");
    expect(cardLink).toHaveAttribute("href", "/help-requests/1");
    expect(cardLink).toHaveClass("cursor-pointer");
  });

  it("应该在悬停时显示正确的样式", () => {
    render(<TestMyHelpRequestCard helpRequest={mockHelpRequest} />);

    const cardLink = screen.getByRole("link");
    expect(cardLink).toHaveClass("hover:shadow-lg");
    expect(cardLink).toHaveClass("hover:border-blue-300");
    expect(cardLink).toHaveClass("dark:hover:border-blue-600");
  });

  it("应该正确处理多个网盘类型标签的显示", () => {
    const multiPanRequest = {
      ...mockHelpRequest,
      pan_types: ["baidu", "quark", "aliyun", "thunder", "other"],
    };

    render(<TestMyHelpRequestCard helpRequest={multiPanRequest} />);

    // 应该显示前3个标签
    expect(screen.getByText("百度网盘")).toBeInTheDocument();
    expect(screen.getByText("夸克网盘")).toBeInTheDocument();

    // 应该显示 +2 表示还有2个
    expect(screen.getByText("+2")).toBeInTheDocument();
  });

  it("应该在没有描述时不显示描述段落", () => {
    const noDescRequest = {
      ...mockHelpRequest,
      description: "",
    };

    render(<TestMyHelpRequestCard helpRequest={noDescRequest} />);

    expect(
      screen.queryByText("需要找到这部电影的高清版本，最好是1080p以上")
    ).not.toBeInTheDocument();
  });

  it("应该正确格式化日期显示", () => {
    const todayRequest = {
      ...mockHelpRequest,
      created_at: new Date().toISOString(),
    };

    render(<TestMyHelpRequestCard helpRequest={todayRequest} />);

    expect(screen.getByText("今天")).toBeInTheDocument();
  });
});

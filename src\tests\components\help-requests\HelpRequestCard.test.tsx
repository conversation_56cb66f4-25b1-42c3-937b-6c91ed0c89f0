/**
 * HelpRequestCard 组件测试
 * 测试求助卡片组件的渲染和交互功能
 */

import { render, screen, fireEvent } from "@testing-library/react";
import { vi, describe, it, expect, beforeEach } from "vitest";
import { useRouter } from "next/navigation";
import HelpRequestCard from "@/components/help-requests/HelpRequestCard";
import { HelpRequest } from "@/types/help-request";

// Mock Next.js router
vi.mock("next/navigation", () => ({
  useRouter: vi.fn(),
}));

// Mock 数据
const mockHelpRequest: HelpRequest = {
  id: "1",
  title: "寻找《测试电影》高清资源",
  description: "需要找到这部电影的高清版本，最好是1080p以上",
  status: "open",
  pan_types: ["baidu", "quark"],
  resource_types: ["video"],
  user: {
    id: "user1",
    username: "testuser",
    level: 2,
  },
  answers_count: 3,
  view_count: 156,
  created_at: "2024-01-15T10:30:00Z",
  updated_at: "2024-01-15T10:30:00Z",
};

const mockPush = vi.fn();

beforeEach(() => {
  (useRouter as any).mockReturnValue({
    push: mockPush,
  });
  vi.clearAllMocks();
});

describe("HelpRequestCard", () => {
  it("应该正确渲染求助卡片的基本信息", () => {
    render(<HelpRequestCard helpRequest={mockHelpRequest} />);

    // 检查标题
    expect(screen.getByText("寻找《测试电影》高清资源")).toBeInTheDocument();

    // 检查描述
    expect(
      screen.getByText("需要找到这部电影的高清版本，最好是1080p以上")
    ).toBeInTheDocument();

    // 检查状态
    expect(screen.getByText("求助中")).toBeInTheDocument();

    // 检查用户信息
    expect(screen.getByText("testuser")).toBeInTheDocument();
    expect(screen.getByText("Lv.2")).toBeInTheDocument();

    // 检查统计信息
    expect(screen.getByText("3 个回答")).toBeInTheDocument();
    expect(screen.getByText("156 次浏览")).toBeInTheDocument();
  });

  it("应该正确渲染网盘类型标签", () => {
    render(<HelpRequestCard helpRequest={mockHelpRequest} />);

    expect(screen.getByText("百度网盘")).toBeInTheDocument();
    expect(screen.getByText("夸克网盘")).toBeInTheDocument();
  });

  it("应该正确渲染资源类型标签", () => {
    render(<HelpRequestCard helpRequest={mockHelpRequest} />);

    expect(screen.getByText("视频")).toBeInTheDocument();
  });

  it("应该在状态为已解决时显示解决图标", () => {
    const solvedRequest = {
      ...mockHelpRequest,
      status: "solved" as const,
    };

    render(<HelpRequestCard helpRequest={solvedRequest} />);

    expect(screen.getByText("已解决")).toBeInTheDocument();
    expect(screen.getByTitle("已解决")).toBeInTheDocument();
  });

  it("应该正确处理多个网盘类型标签的显示", () => {
    const multiPanRequest = {
      ...mockHelpRequest,
      pan_types: ["baidu", "quark", "aliyun", "thunder", "other"],
    };

    render(<HelpRequestCard helpRequest={multiPanRequest} />);

    // 应该显示前3个标签
    expect(screen.getByText("百度网盘")).toBeInTheDocument();
    expect(screen.getByText("夸克网盘")).toBeInTheDocument();
    expect(screen.getByText("阿里云盘")).toBeInTheDocument();

    // 应该显示 +2 表示还有2个
    expect(screen.getByText("+2")).toBeInTheDocument();
  });

  it("应该正确处理多个资源类型标签的显示", () => {
    const multiResourceRequest = {
      ...mockHelpRequest,
      resource_types: ["video", "audio", "document"],
    };

    render(<HelpRequestCard helpRequest={multiResourceRequest} />);

    // 应该显示前2个标签
    expect(screen.getByText("视频")).toBeInTheDocument();
    expect(screen.getByText("音频")).toBeInTheDocument();

    // 应该显示 +1 表示还有1个
    expect(screen.getByText("+1")).toBeInTheDocument();
  });

  it("应该在showUser为false时隐藏用户信息", () => {
    render(<HelpRequestCard helpRequest={mockHelpRequest} showUser={false} />);

    expect(screen.queryByText("testuser")).not.toBeInTheDocument();
    expect(screen.queryByText("Lv.2")).not.toBeInTheDocument();
  });

  it("应该正确格式化日期显示", () => {
    const todayRequest = {
      ...mockHelpRequest,
      created_at: new Date().toISOString(),
    };

    render(<HelpRequestCard helpRequest={todayRequest} />);

    expect(screen.getByText("今天")).toBeInTheDocument();
  });

  it("应该整个卡片都可以点击", () => {
    render(<HelpRequestCard helpRequest={mockHelpRequest} />);

    const cardLink = screen.getByRole("link");
    expect(cardLink).toHaveAttribute("href", "/help-requests/1");
    expect(cardLink).toHaveClass("cursor-pointer");
  });

  it("应该在悬停时显示正确的样式", () => {
    render(<HelpRequestCard helpRequest={mockHelpRequest} />);

    const cardLink = screen.getByRole("link");
    expect(cardLink).toHaveClass("hover:shadow-lg");
    expect(cardLink).toHaveClass("hover:border-blue-300");
    expect(cardLink).toHaveClass("dark:hover:border-blue-600");
  });

  it("应该正确应用自定义className", () => {
    const customClass = "custom-test-class";
    render(
      <HelpRequestCard helpRequest={mockHelpRequest} className={customClass} />
    );

    const cardLink = screen.getByRole("link");
    expect(cardLink).toHaveClass(customClass);
  });

  it("应该在没有描述时不显示描述段落", () => {
    const noDescRequest = {
      ...mockHelpRequest,
      description: "",
    };

    render(<HelpRequestCard helpRequest={noDescRequest} />);

    expect(
      screen.queryByText("需要找到这部电影的高清版本，最好是1080p以上")
    ).not.toBeInTheDocument();
  });

  it("应该正确处理用户等级为1的情况", () => {
    const level1Request = {
      ...mockHelpRequest,
      user: {
        ...mockHelpRequest.user,
        level: 1,
      },
    };

    render(<HelpRequestCard helpRequest={level1Request} />);

    expect(screen.getByText("testuser")).toBeInTheDocument();
    expect(screen.queryByText("Lv.1")).not.toBeInTheDocument();
  });
});

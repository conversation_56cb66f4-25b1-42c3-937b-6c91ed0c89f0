/**
 * MyAnswerCard 组件测试
 * 测试我的回答卡片组件的渲染和交互功能
 */

import { render, screen } from "@testing-library/react";
import { vi, describe, it, expect, beforeEach } from "vitest";
import { useRouter } from "next/navigation";
import { HelpAnswer } from "@/types/help-request";

// Mock Next.js router
vi.mock("next/navigation", () => ({
  useRouter: vi.fn(),
}));

// Mock 数据
const mockAnswer: HelpAnswer = {
  id: "1",
  help_request_id: "req1",
  user_id: "user1",
  resource_title: "测试电影高清版",
  description: "这是一个很好的资源，画质清晰",
  pan_type: "baidu",
  resource_link: "https://pan.baidu.com/s/test",
  extraction_code: "test",
  is_best: false,
  is_parsed: true,
  created_at: "2024-01-15T10:30:00Z",
  updated_at: "2024-01-15T10:30:00Z",
};

const mockPush = vi.fn();

beforeEach(() => {
  (useRouter as any).mockReturnValue({
    push: mockPush,
  });
  vi.clearAllMocks();
});

// 创建一个测试组件来包装 MyAnswerCard
function TestMyAnswerCard({ answer }: { answer: HelpAnswer }) {
  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString("zh-CN");
  };

  const PAN_TYPE_MAP = {
    baidu: "百度网盘",
    quark: "夸克网盘",
    aliyun: "阿里云盘",
    thunder: "迅雷网盘",
  };

  return (
    <a
      href={`/help-requests/${answer.help_request_id}`}
      className={`block bg-card-background border rounded-lg p-4 hover:shadow-lg hover:border-blue-300 dark:hover:border-blue-600 transition-all duration-200 cursor-pointer ${
        answer.is_best
          ? "border-green-500 bg-green-50 dark:bg-green-900/10"
          : "border-border-color"
      }`}
    >
      {answer.is_best && (
        <div className="flex items-center mb-2 text-green-600">
          <span className="text-sm font-medium">🌟 最佳答案</span>
        </div>
      )}

      <h3 className="text-gray-900 dark:text-gray-100 hover:text-blue-600 transition-colors font-medium">
        回答了求助问题
      </h3>

      <div className="mt-2 text-sm text-secondary-text">
        <div className="flex items-center mb-1">
          <span className="mr-2">网盘类型:</span>
          <span className="px-2 py-1 bg-blue-100 text-blue-800 dark:bg-blue-900/20 dark:text-blue-400 rounded text-xs">
            {PAN_TYPE_MAP[answer.pan_type as keyof typeof PAN_TYPE_MAP]}
          </span>
          {answer.is_parsed && (
            <span className="ml-2 px-2 py-1 bg-green-100 text-green-800 dark:bg-green-900/20 dark:text-green-400 rounded text-xs">
              已解析
            </span>
          )}
        </div>
        {answer.description && (
          <p className="text-secondary-text line-clamp-2 mb-2">
            {answer.description}
          </p>
        )}
        <div className="text-xs text-secondary-text">
          回答时间: {formatDate(answer.created_at)}
        </div>
      </div>
    </a>
  );
}

describe("MyAnswerCard", () => {
  it("应该正确渲染我的回答卡片的基本信息", () => {
    render(<TestMyAnswerCard answer={mockAnswer} />);

    // 检查标题
    expect(screen.getByText("回答了求助问题")).toBeInTheDocument();

    // 检查描述
    expect(
      screen.getByText("这是一个很好的资源，画质清晰")
    ).toBeInTheDocument();

    // 检查网盘类型
    expect(screen.getByText("网盘类型:")).toBeInTheDocument();
    expect(screen.getByText("百度网盘")).toBeInTheDocument();

    // 检查解析状态
    expect(screen.getByText("已解析")).toBeInTheDocument();

    // 检查回答时间
    expect(screen.getByText(/回答时间:/)).toBeInTheDocument();
  });

  it("应该在is_best为true时显示最佳答案标识", () => {
    const bestAnswer = {
      ...mockAnswer,
      is_best: true,
    };

    render(<TestMyAnswerCard answer={bestAnswer} />);

    expect(screen.getByText("🌟 最佳答案")).toBeInTheDocument();
  });

  it("应该在is_best为true时应用特殊样式", () => {
    const bestAnswer = {
      ...mockAnswer,
      is_best: true,
    };

    render(<TestMyAnswerCard answer={bestAnswer} />);

    const cardLink = screen.getByRole("link");
    expect(cardLink).toHaveClass("border-green-500");
    expect(cardLink).toHaveClass("bg-green-50");
    expect(cardLink).toHaveClass("dark:bg-green-900/10");
  });

  it("应该在is_parsed为false时不显示已解析标签", () => {
    const unparsedAnswer = {
      ...mockAnswer,
      is_parsed: false,
    };

    render(<TestMyAnswerCard answer={unparsedAnswer} />);

    expect(screen.queryByText("已解析")).not.toBeInTheDocument();
  });

  it("应该正确显示不同的网盘类型", () => {
    const quarkAnswer = {
      ...mockAnswer,
      pan_type: "quark" as const,
    };

    render(<TestMyAnswerCard answer={quarkAnswer} />);

    expect(screen.getByText("夸克网盘")).toBeInTheDocument();
  });

  it("应该整个卡片都可以点击", () => {
    render(<TestMyAnswerCard answer={mockAnswer} />);

    const cardLink = screen.getByRole("link");
    expect(cardLink).toHaveAttribute("href", "/help-requests/req1");
    expect(cardLink).toHaveClass("cursor-pointer");
  });

  it("应该在悬停时显示正确的样式", () => {
    render(<TestMyAnswerCard answer={mockAnswer} />);

    const cardLink = screen.getByRole("link");
    expect(cardLink).toHaveClass("hover:shadow-lg");
    expect(cardLink).toHaveClass("hover:border-blue-300");
    expect(cardLink).toHaveClass("dark:hover:border-blue-600");
  });

  it("应该在没有描述时不显示描述段落", () => {
    const noDescAnswer = {
      ...mockAnswer,
      description: "",
    };

    render(<TestMyAnswerCard answer={noDescAnswer} />);

    expect(
      screen.queryByText("这是一个很好的资源，画质清晰")
    ).not.toBeInTheDocument();
  });

  it("应该正确格式化日期显示", () => {
    render(<TestMyAnswerCard answer={mockAnswer} />);

    // 检查日期格式化
    const dateText = screen.getByText(/回答时间:/);
    expect(dateText).toBeInTheDocument();
    expect(dateText.textContent).toMatch(/回答时间: \d{4}\/\d{1,2}\/\d{1,2}/);
  });

  it("应该正确处理阿里云盘类型", () => {
    const aliyunAnswer = {
      ...mockAnswer,
      pan_type: "aliyun" as const,
    };

    render(<TestMyAnswerCard answer={aliyunAnswer} />);

    expect(screen.getByText("阿里云盘")).toBeInTheDocument();
  });

  it("应该正确处理迅雷网盘类型", () => {
    const thunderAnswer = {
      ...mockAnswer,
      pan_type: "thunder" as const,
    };

    render(<TestMyAnswerCard answer={thunderAnswer} />);

    expect(screen.getByText("迅雷网盘")).toBeInTheDocument();
  });

  it("应该在非最佳答案时应用默认边框样式", () => {
    render(<TestMyAnswerCard answer={mockAnswer} />);

    const cardLink = screen.getByRole("link");
    expect(cardLink).toHaveClass("border-border-color");
    expect(cardLink).not.toHaveClass("border-green-500");
  });
});
